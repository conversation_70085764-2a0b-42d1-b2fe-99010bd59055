from app.db.session import engine
from app.models.workflow import Base
from app.models.workflow_rating import Base as WorkflowRatingBase
from sqlalchemy import MetaData

def init_db():
    Base.metadata.create_all(bind=engine)
    WorkflowRatingBase.metadata.create_all(bind=engine)
    metadata = MetaData()
    metadata.reflect(bind=engine)
    print(metadata.tables)


if __name__ == "__main__":
    init_db()
