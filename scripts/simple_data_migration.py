"""
Simple Data Migration Script: Copy data from old tables to new tables

This script only migrates data (assumes new tables already exist):
- "test-workflows" -> "test-workflows-available-nodes"
- "test-workflow-templates" -> "test-workflow-templates-available-nodes"

Sets available_nodes = [] for all existing records.
"""

import sys
import os
from sqlalchemy import create_engine, text

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.core.config import settings

def get_database_url():
    """Get database URL from settings"""
    return settings.DATABASE_URL

def migrate_data():
    """Simple data migration using SQL"""
    print("🚀 Starting simple data migration...")
    
    try:
        # Create database engine
        database_url = get_database_url()
        engine = create_engine(database_url)
        
        print(f"🔗 Connected to database")
        
        with engine.connect() as conn:
            # Migrate workflows
            print("\n🔄 Migrating workflows...")
            
            # Check if old workflows table exists
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'test-workflows'
                );
            """))
            
            if result.scalar():
                # Insert workflows with available_nodes = []
                workflow_result = conn.execute(text("""
                    INSERT INTO "test-workflows-available-nodes" 
                    SELECT *, '[]'::json as available_nodes 
                    FROM "test-workflows"
                    ON CONFLICT (id) DO NOTHING;
                """))
                print(f"✅ Migrated workflows (affected rows: {workflow_result.rowcount})")
            else:
                print("⚠️  Old workflows table doesn't exist")
            
            # Migrate workflow templates
            print("\n🔄 Migrating workflow templates...")
            
            # Check if old templates table exists
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'test-workflow-templates'
                );
            """))
            
            if result.scalar():
                # Insert templates with available_nodes = []
                template_result = conn.execute(text("""
                    INSERT INTO "test-workflow-templates-available-nodes" 
                    SELECT *, '[]'::json as available_nodes 
                    FROM "test-workflow-templates"
                    ON CONFLICT (id) DO NOTHING;
                """))
                print(f"✅ Migrated workflow templates (affected rows: {template_result.rowcount})")
            else:
                print("⚠️  Old workflow templates table doesn't exist")
            
            # Commit the transaction
            conn.commit()
            
        print("\n🎉 Simple data migration completed successfully!")
        
    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    migrate_data()
