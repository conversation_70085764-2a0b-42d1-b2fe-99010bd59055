"""
Data Migration Script: Copy data from old tables to new tables with available_nodes field

This script copies all data from:
- "test-workflows" -> "test-workflows-available-nodes"
- "test-workflow-templates" -> "test-workflow-templates-available-nodes"

And sets available_nodes = [] (empty list) for all existing records.
"""

import sys
import os
import json
from datetime import datetime
from sqlalchemy import create_engine, text, MetaData, Table, Column, String, DateTime, Integer, Float, Boolean, JSON, Enum, ARRAY
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import sessionmaker

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.core.config import settings
from app.utils.constants.constants import (
    WorkflowCategoryEnum,
    WorkflowOwnerTypeEnum,
    WorkflowVisibilityEnum,
    WorkflowStatusEnum,
)

def get_database_url():
    """Get database URL from settings"""
    return settings.DATABASE_URL

def create_new_tables_if_not_exist(engine):
    """Create the new tables with available_nodes field if they don't exist"""
    metadata = MetaData()
    
    # Define new workflow table structure
    new_workflows_table = Table(
        'test-workflows-available-nodes',
        metadata,
        Column('id', String, primary_key=True),
        Column('name', String(255), nullable=False),
        Column('description', String, nullable=True),
        Column('image_url', String, nullable=True),
        Column('workflow_url', String, nullable=False),
        Column('builder_url', String, nullable=False),
        Column('start_nodes', JSON, nullable=False, default=list),
        Column('available_nodes', JSON, nullable=False, default=list),  # New field
        Column('owner_id', String, nullable=False),
        Column('user_ids', ARRAY(String), nullable=True),
        Column('owner_type', Enum(WorkflowOwnerTypeEnum), nullable=False),
        Column('workflow_template_id', String, nullable=True),
        Column('template_owner_id', String, nullable=True),
        Column('url', String, nullable=True),
        Column('is_imported', Boolean, default=False),
        Column('version', String(50), nullable=True, default="1.0.0"),
        Column('is_changes_marketplace', Boolean, nullable=True),
        Column('use_count', Integer, default=0),
        Column('average_rating', Float, default=0.0),
        Column('visibility', Enum(WorkflowVisibilityEnum), nullable=True, default=WorkflowVisibilityEnum.PRIVATE),
        Column('category', Enum(WorkflowCategoryEnum), nullable=True),
        Column('tags', JSON, nullable=True),
        Column('status', Enum(WorkflowStatusEnum), nullable=False, default=WorkflowStatusEnum.ACTIVE),
        Column('created_at', DateTime, default=datetime.utcnow, nullable=False),
        Column('updated_at', DateTime, default=datetime.utcnow, nullable=False),
    )
    
    # Define new workflow template table structure
    new_templates_table = Table(
        'test-workflow-templates-available-nodes',
        metadata,
        Column('id', String, primary_key=True),
        Column('name', String(255), nullable=False),
        Column('description', String, nullable=True),
        Column('image_url', String, nullable=True),
        Column('workflow_url', String, nullable=False),
        Column('builder_url', String, nullable=False),
        Column('start_nodes', JSON, nullable=False, default=list),
        Column('available_nodes', JSON, nullable=False, default=list),  # New field
        Column('owner_id', String, nullable=False),
        Column('use_count', Integer, default=0),
        Column('execution_count', Integer, default=0),
        Column('average_rating', Float, default=0),
        Column('category', Enum(WorkflowCategoryEnum), nullable=True),
        Column('tags', JSON, nullable=True),
        Column('version', String(50), nullable=False, default="1.0.0"),
        Column('status', Enum(WorkflowStatusEnum), nullable=False, default=WorkflowStatusEnum.ACTIVE),
        Column('visibility', Enum(WorkflowVisibilityEnum), nullable=True, default=WorkflowVisibilityEnum.PUBLIC),
        Column('is_customizable', Boolean, default=False),
        Column('created_at', DateTime, default=datetime.utcnow, nullable=False),
        Column('updated_at', DateTime, default=datetime.utcnow, nullable=False),
    )
    
    # Create tables if they don't exist
    metadata.create_all(engine, checkfirst=True)
    print("✅ New tables created successfully (if they didn't exist)")
    
    return new_workflows_table, new_templates_table

def migrate_workflows_data(engine):
    """Migrate data from test-workflows to test-workflows-available-nodes"""
    print("\n🔄 Starting workflows data migration...")
    
    # Check if old table exists
    with engine.connect() as conn:
        result = conn.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'test-workflows'
            );
        """))
        old_table_exists = result.scalar()
        
        if not old_table_exists:
            print("⚠️  Old table 'test-workflows' does not exist. Skipping workflows migration.")
            return
    
    # Get all data from old table
    with engine.connect() as conn:
        result = conn.execute(text("SELECT * FROM \"test-workflows\""))
        old_workflows = result.fetchall()
        columns = result.keys()
        
        print(f"📊 Found {len(old_workflows)} workflows to migrate")
        
        if len(old_workflows) == 0:
            print("ℹ️  No workflows to migrate")
            return
        
        # Insert data into new table
        migrated_count = 0
        for workflow in old_workflows:
            # Convert row to dict
            workflow_dict = dict(zip(columns, workflow))
            
            # Add available_nodes field with empty list
            workflow_dict['available_nodes'] = []
            
            # Prepare insert statement
            columns_str = ', '.join([f'"{col}"' for col in workflow_dict.keys()])
            values_str = ', '.join([f':{col}' for col in workflow_dict.keys()])
            
            insert_sql = f"""
                INSERT INTO "test-workflows-available-nodes" ({columns_str})
                VALUES ({values_str})
                ON CONFLICT (id) DO NOTHING
            """
            
            try:
                conn.execute(text(insert_sql), workflow_dict)
                migrated_count += 1
            except Exception as e:
                print(f"❌ Error migrating workflow {workflow_dict.get('id', 'unknown')}: {str(e)}")
        
        conn.commit()
        print(f"✅ Successfully migrated {migrated_count} workflows")

def migrate_templates_data(engine):
    """Migrate data from test-workflow-templates to test-workflow-templates-available-nodes"""
    print("\n🔄 Starting workflow templates data migration...")
    
    # Check if old table exists
    with engine.connect() as conn:
        result = conn.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'test-workflow-templates'
            );
        """))
        old_table_exists = result.scalar()
        
        if not old_table_exists:
            print("⚠️  Old table 'test-workflow-templates' does not exist. Skipping templates migration.")
            return
    
    # Get all data from old table
    with engine.connect() as conn:
        result = conn.execute(text("SELECT * FROM \"test-workflow-templates\""))
        old_templates = result.fetchall()
        columns = result.keys()
        
        print(f"📊 Found {len(old_templates)} workflow templates to migrate")
        
        if len(old_templates) == 0:
            print("ℹ️  No workflow templates to migrate")
            return
        
        # Insert data into new table
        migrated_count = 0
        for template in old_templates:
            # Convert row to dict
            template_dict = dict(zip(columns, template))
            
            # Add available_nodes field with empty list
            template_dict['available_nodes'] = []
            
            # Prepare insert statement
            columns_str = ', '.join([f'"{col}"' for col in template_dict.keys()])
            values_str = ', '.join([f':{col}' for col in template_dict.keys()])
            
            insert_sql = f"""
                INSERT INTO "test-workflow-templates-available-nodes" ({columns_str})
                VALUES ({values_str})
                ON CONFLICT (id) DO NOTHING
            """
            
            try:
                conn.execute(text(insert_sql), template_dict)
                migrated_count += 1
            except Exception as e:
                print(f"❌ Error migrating template {template_dict.get('id', 'unknown')}: {str(e)}")
        
        conn.commit()
        print(f"✅ Successfully migrated {migrated_count} workflow templates")

def main():
    """Main migration function"""
    print("🚀 Starting data migration from old tables to new tables with available_nodes field")
    print("=" * 80)
    
    try:
        # Create database engine
        database_url = get_database_url()
        engine = create_engine(database_url)
        
        print(f"🔗 Connected to database: {database_url.split('@')[1] if '@' in database_url else 'localhost'}")
        
        # Create new tables if they don't exist
        create_new_tables_if_not_exist(engine)
        
        # Migrate workflows data
        migrate_workflows_data(engine)
        
        # Migrate templates data
        migrate_templates_data(engine)
        
        print("\n" + "=" * 80)
        print("🎉 Data migration completed successfully!")
        print("\nNext steps:")
        print("1. Verify the data in your new tables")
        print("2. Update your application to use the new table names")
        print("3. Consider backing up and dropping the old tables when ready")
        
    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
