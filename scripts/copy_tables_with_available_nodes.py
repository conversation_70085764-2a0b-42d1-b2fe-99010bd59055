"""
Migration Script: Copy data from old tables to new tables with available_nodes field

This script copies ALL data from:
- "test-workflows" -> "test-workflows-available-nodes"  
- "test-workflow-templates" -> "test-workflow-templates-available-nodes"

And adds the new available_nodes field with default empty list [] for all records.

Usage:
    python scripts/copy_tables_with_available_nodes.py "postgresql://user:password@host:port/database"
"""

import sys
import os
from sqlalchemy import create_engine, text, inspect

def check_table_exists(conn, table_name):
    """Check if a table exists in the database"""
    result = conn.execute(text("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = :table_name
        );
    """), {"table_name": table_name})
    return result.scalar()

def get_table_columns(conn, table_name):
    """Get all column names from a table"""
    result = conn.execute(text("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = :table_name
        ORDER BY ordinal_position;
    """), {"table_name": table_name})
    return [row[0] for row in result.fetchall()]

def copy_workflows_data(conn):
    """Copy data from test-workflows to test-workflows-available-nodes"""
    print("\n🔄 Copying workflows data...")
    
    old_table = "test-workflows"
    new_table = "test-workflows-available-nodes"
    
    # Check if old table exists
    if not check_table_exists(conn, old_table):
        print(f"⚠️  Source table '{old_table}' does not exist. Skipping workflows migration.")
        return
    
    # Check if new table exists
    if not check_table_exists(conn, new_table):
        print(f"❌ Destination table '{new_table}' does not exist. Please create it first.")
        return
    
    # Get columns from old table
    old_columns = get_table_columns(conn, old_table)
    print(f"📋 Found {len(old_columns)} columns in source table: {old_columns}")
    
    # Get columns from new table
    new_columns = get_table_columns(conn, new_table)
    print(f"📋 Found {len(new_columns)} columns in destination table: {new_columns}")
    
    # Verify available_nodes exists in new table
    if 'available_nodes' not in new_columns:
        print(f"❌ Column 'available_nodes' not found in destination table '{new_table}'")
        return
    
    # Get count of records in old table
    count_result = conn.execute(text(f'SELECT COUNT(*) FROM "{old_table}"'))
    total_records = count_result.scalar()
    print(f"📊 Found {total_records} records to copy")
    
    if total_records == 0:
        print("ℹ️  No records to copy")
        return
    
    # Prepare column list (excluding available_nodes from old table)
    copy_columns = [col for col in old_columns if col in new_columns and col != 'available_nodes']
    columns_str = ', '.join([f'"{col}"' for col in copy_columns])
    
    # Copy data with available_nodes = []
    copy_sql = f"""
        INSERT INTO "{new_table}" ({columns_str}, "available_nodes")
        SELECT {columns_str}, '[]'::json as available_nodes
        FROM "{old_table}"
        ON CONFLICT (id) DO NOTHING;
    """
    
    try:
        result = conn.execute(text(copy_sql))
        copied_count = result.rowcount
        print(f"✅ Successfully copied {copied_count} workflow records")
        
        if copied_count < total_records:
            print(f"ℹ️  {total_records - copied_count} records were skipped (likely due to conflicts)")
            
    except Exception as e:
        print(f"❌ Error copying workflows: {str(e)}")
        raise

def copy_templates_data(conn):
    """Copy data from test-workflow-templates to test-workflow-templates-available-nodes"""
    print("\n🔄 Copying workflow templates data...")
    
    old_table = "test-workflow-templates"
    new_table = "test-workflow-templates-available-nodes"
    
    # Check if old table exists
    if not check_table_exists(conn, old_table):
        print(f"⚠️  Source table '{old_table}' does not exist. Skipping templates migration.")
        return
    
    # Check if new table exists
    if not check_table_exists(conn, new_table):
        print(f"❌ Destination table '{new_table}' does not exist. Please create it first.")
        return
    
    # Get columns from old table
    old_columns = get_table_columns(conn, old_table)
    print(f"📋 Found {len(old_columns)} columns in source table: {old_columns}")
    
    # Get columns from new table
    new_columns = get_table_columns(conn, new_table)
    print(f"📋 Found {len(new_columns)} columns in destination table: {new_columns}")
    
    # Verify available_nodes exists in new table
    if 'available_nodes' not in new_columns:
        print(f"❌ Column 'available_nodes' not found in destination table '{new_table}'")
        return
    
    # Get count of records in old table
    count_result = conn.execute(text(f'SELECT COUNT(*) FROM "{old_table}"'))
    total_records = count_result.scalar()
    print(f"📊 Found {total_records} records to copy")
    
    if total_records == 0:
        print("ℹ️  No records to copy")
        return
    
    # Prepare column list (excluding available_nodes from old table)
    copy_columns = [col for col in old_columns if col in new_columns and col != 'available_nodes']
    columns_str = ', '.join([f'"{col}"' for col in copy_columns])
    
    # Copy data with available_nodes = []
    copy_sql = f"""
        INSERT INTO "{new_table}" ({columns_str}, "available_nodes")
        SELECT {columns_str}, '[]'::json as available_nodes
        FROM "{old_table}"
        ON CONFLICT (id) DO NOTHING;
    """
    
    try:
        result = conn.execute(text(copy_sql))
        copied_count = result.rowcount
        print(f"✅ Successfully copied {copied_count} template records")
        
        if copied_count < total_records:
            print(f"ℹ️  {total_records - copied_count} records were skipped (likely due to conflicts)")
            
    except Exception as e:
        print(f"❌ Error copying templates: {str(e)}")
        raise

def migrate_data(database_url):
    """Main migration function"""
    print("🚀 Starting table data migration with available_nodes field")
    print("=" * 80)
    print(f"🔗 Connecting to database: {database_url.split('@')[1] if '@' in database_url else 'localhost'}")
    
    try:
        # Create database engine
        engine = create_engine(database_url)
        
        with engine.connect() as conn:
            # Start transaction
            trans = conn.begin()
            
            try:
                # Copy workflows data
                copy_workflows_data(conn)
                
                # Copy templates data  
                copy_templates_data(conn)
                
                # Commit transaction
                trans.commit()
                
                print("\n" + "=" * 80)
                print("🎉 Data migration completed successfully!")
                print("\nNext steps:")
                print("1. Verify the data in your new tables:")
                print("   - test-workflows-available-nodes")
                print("   - test-workflow-templates-available-nodes")
                print("2. Update your table names in constants/table_names.py:")
                print("   WORKFLOW_TABLE = 'test-workflows-available-nodes'")
                print("   WORKFLOW_TEMPLATE_TABLE = 'test-workflow-templates-available-nodes'")
                print("3. Test your application with the new tables")
                print("4. Consider backing up and dropping old tables when ready")
                
            except Exception as e:
                trans.rollback()
                raise e
                
    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

def main():
    if len(sys.argv) != 2:
        print("❌ Usage: python scripts/copy_tables_with_available_nodes.py <database_url>")
        print("\nExample:")
        print("python scripts/copy_tables_with_available_nodes.py 'postgresql://user:password@localhost:5432/database'")
        print("\nThis script will copy ALL data from:")
        print("- test-workflows → test-workflows-available-nodes")
        print("- test-workflow-templates → test-workflow-templates-available-nodes")
        print("\nAnd add available_nodes = [] for all records.")
        sys.exit(1)
    
    database_url = sys.argv[1]
    migrate_data(database_url)

if __name__ == "__main__":
    main()
